local a = {

    demon_minotaur_smoke_hit_run = {from=1, to=7, prefix='demon_minotaur_smoke_hit'},

    demon_minotaur_charge_dust_a = {from=1, to=14, prefix='demon_minotaur_charge_dust_a'},

    demon_minotaur_charge_dust_b = {from=1, to=44, prefix='demon_minotaur_charge_dust_b'},

    demon_minotaur_hit_run = {from=1, to=10, prefix='demon_minotaur_hit'},

    demon_minotaur_rebote_fx_run = {from=1, to=39, prefix='demon_minotaur_rebote_fx'},

    demon_minotaur_unit_idle = {from=1, to=2, prefix='demon_minotaur_unit'},
    demon_minotaur_unit_walk = {from=3, to=34, prefix='demon_minotaur_unit'},
    demon_minotaur_unit_walk_down = {from=35, to=66, prefix='demon_minotaur_unit'},
    demon_minotaur_unit_walk_up = {from=67, to=98, prefix='demon_minotaur_unit'},
    demon_minotaur_unit_melee_1 = {from=99, to=156, prefix='demon_minotaur_unit'},
    demon_minotaur_unit_melee_2 = {from=157, to=217, prefix='demon_minotaur_unit'},
    demon_minotaur_unit_charge = {from=218, to=227, prefix='demon_minotaur_unit'},
    demon_minotaur_unit_charge_down = {from=228, to=247, prefix='demon_minotaur_unit'},
    demon_minotaur_unit_charge_up = {from=248, to=267, prefix='demon_minotaur_unit'},
    demon_minotaur_unit_charge_attack_in = {from=268, to=291, prefix='demon_minotaur_unit'},
    demon_minotaur_unit_rebote = {from=292, to=321, prefix='demon_minotaur_unit'},
    demon_minotaur_unit_rebote_frente = {from=322, to=349, prefix='demon_minotaur_unit'},
    demon_minotaur_unit_death = {from=350, to=387, prefix='demon_minotaur_unit'},
    demon_minotaur_unit_charge_down_attack_in = {from=388, to=411, prefix='demon_minotaur_unit'},

    demon_minotaur_unit_charge_attack_out = {from=350, to=387, prefix='demon_minotaur_unit'},

}

return a
